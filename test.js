// 引入getUrlParams函数
const getUrlParams = require('./getUrlParams.js');

/**
 * 测试用例集合
 * 包含各种URL格式的测试
 */
const testCases = [
  // 基础query参数测试
  {
    name: '基础query参数',
    url: 'https://example.com?name=张三&age=25&city=北京',
    expected: { name: '张三', age: '25', city: '北京' },
    getParam: 'name',
    expectedParam: '张三'
  },
  
  // Hash路由参数测试
  {
    name: 'Hash路由参数 (#/path?params)',
    url: 'https://example.com/#/user?id=123&tab=profile',
    expected: { id: '123', tab: 'profile' },
    getParam: 'id',
    expectedParam: '123'
  },
  
  // Hash直接参数测试
  {
    name: 'Hash直接参数 (#params)',
    url: 'https://example.com/#name=李四&role=admin',
    expected: { name: '李四', role: 'admin' },
    getParam: 'role',
    expectedParam: 'admin'
  },
  
  // 混合参数测试（query + hash）
  {
    name: '混合参数（query + hash，hash优先）',
    url: 'https://example.com?name=王五&age=30#name=赵六&city=上海',
    expected: { name: '赵六', age: '30', city: '上海' },
    getParam: 'name',
    expectedParam: '赵六'
  },
  
  // 中文参数编码测试
  {
    name: '中文参数编码',
    url: 'https://example.com?keyword=%E4%B8%AD%E6%96%87%E6%90%9C%E7%B4%A2&type=search',
    expected: { keyword: '中文搜索', type: 'search' },
    getParam: 'keyword',
    expectedParam: '中文搜索'
  },
  
  // 特殊字符参数测试
  {
    name: '特殊字符参数',
    url: 'https://example.com?email=test%40example.com&symbol=%26%3D%3F',
    expected: { email: '<EMAIL>', symbol: '&=?' },
    getParam: 'email',
    expectedParam: '<EMAIL>'
  },
  
  // 空值参数测试
  {
    name: '空值参数',
    url: 'https://example.com?empty=&name=test&null',
    expected: { empty: '', name: 'test', null: '' },
    getParam: 'empty',
    expectedParam: ''
  },
  
  // 复杂hash路由测试
  {
    name: '复杂hash路由',
    url: 'https://example.com/app#/dashboard/analytics?from=2023-01-01&to=2023-12-31&chart=line',
    expected: { from: '2023-01-01', to: '2023-12-31', chart: 'line' },
    getParam: 'from',
    expectedParam: '2023-01-01'
  },
  
  // 无参数URL测试
  {
    name: '无参数URL',
    url: 'https://example.com/page',
    expected: {},
    getParam: 'nonexistent',
    expectedParam: null
  },
  
  // 相对路径测试
  {
    name: '相对路径',
    url: '/page?id=456&status=active',
    expected: { id: '456', status: 'active' },
    getParam: 'status',
    expectedParam: 'active'
  },
  
  // 数组参数测试
  {
    name: '数组参数',
    url: 'https://example.com?tags=javascript&tags=vue&tags=react',
    expected: { tags: 'react' }, // URLSearchParams会取最后一个值
    getParam: 'tags',
    expectedParam: 'react'
  },
  
  // 端口号URL测试
  {
    name: '带端口号URL',
    url: 'http://localhost:3000/app?debug=true&env=development',
    expected: { debug: 'true', env: 'development' },
    getParam: 'debug',
    expectedParam: 'true'
  }
];

/**
 * 运行测试函数
 */
function runTests() {
  console.log('🚀 开始运行URL参数解析测试...\n');
  
  let passedTests = 0;
  let totalTests = testCases.length;
  
  testCases.forEach((testCase, index) => {
    console.log(`📋 测试 ${index + 1}: ${testCase.name}`);
    console.log(`🔗 URL: ${testCase.url}`);
    
    try {
      // 测试获取所有参数
      const allParams = getUrlParams(testCase.url);
      const allParamsMatch = JSON.stringify(allParams) === JSON.stringify(testCase.expected);
      
      // 测试获取单个参数
      const singleParam = getUrlParams(testCase.url, testCase.getParam);
      const singleParamMatch = singleParam === testCase.expectedParam;
      
      if (allParamsMatch && singleParamMatch) {
        console.log('✅ 测试通过');
        passedTests++;
      } else {
        console.log('❌ 测试失败');
        if (!allParamsMatch) {
          console.log('   期望所有参数:', testCase.expected);
          console.log('   实际所有参数:', allParams);
        }
        if (!singleParamMatch) {
          console.log(`   期望参数'${testCase.getParam}':`, testCase.expectedParam);
          console.log(`   实际参数'${testCase.getParam}':`, singleParam);
        }
      }
      
    } catch (error) {
      console.log('❌ 测试出错:', error.message);
    }
    
    console.log(''); // 空行分隔
  });
  
  // 输出测试结果统计
  console.log('📊 测试结果统计:');
  console.log(`   通过: ${passedTests}/${totalTests}`);
  console.log(`   成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试都通过了！函数工作正常。');
  } else {
    console.log('⚠️  有测试失败，请检查函数实现。');
  }
}

// 运行测试
runTests();
